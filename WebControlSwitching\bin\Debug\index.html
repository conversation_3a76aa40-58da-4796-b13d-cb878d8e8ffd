<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单的HTML与C#交互示例</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            min-height: 100vh;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2em;
        }

        .section {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        input[type="text"], input[type="number"] {
            padding: 8px;
            border: none;
            border-radius: 5px;
            margin: 5px;
            font-size: 14px;
            width: 100px;
        }

        .result {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            min-height: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 HTML与C#交互示例</h1>

        <div class="section">
            <h3>1. 基本消息交互</h3>
            <button class="button" onclick="callShowAlert()">显示C#消息框</button>
            <button class="button" onclick="getCurrentTime()">获取C#当前时间</button>
            <div class="result" id="timeResult">点击按钮获取时间</div>
        </div>

        <div class="section">
            <h3>2. 计算器功能</h3>
            <input type="number" id="num1" placeholder="数字1" value="10">
            <select id="operation">
                <option value="add">+</option>
                <option value="subtract">-</option>
                <option value="multiply">×</option>
                <option value="divide">÷</option>
            </select>
            <input type="number" id="num2" placeholder="数字2" value="5">
            <button class="button" onclick="calculate()">计算</button>
            <div class="result" id="calcResult">计算结果将显示在这里</div>
        </div>

        <div class="section">
            <h3>3. 窗体控制</h3>
            <input type="text" id="titleInput" placeholder="输入新标题" value="新窗体标题">
            <button class="button" onclick="changeTitle()">更改窗体标题</button>
        </div>

        <div class="section">
            <h3>4. C#到JavaScript通信</h3>
            <div class="result" id="messageFromCSharp">等待C#消息...</div>
        </div>
    </div>

    <script>
        // JavaScript调用C#方法：显示消息框
        function callShowAlert() {
            try {
                window.external.ShowAlert('这是从JavaScript发送到C#的消息！');
            } catch (e) {
                alert('调用C#方法失败: ' + e.message);
            }
        }

        // JavaScript调用C#方法：获取当前时间
        function getCurrentTime() {
            try {
                var time = window.external.GetCurrentTime();
                document.getElementById('timeResult').innerHTML = '当前时间: ' + time;
            } catch (e) {
                document.getElementById('timeResult').innerHTML = '获取时间失败: ' + e.message;
            }
        }

        // JavaScript调用C#方法：计算功能
        function calculate() {
            try {
                var num1 = parseInt(document.getElementById('num1').value);
                var num2 = parseInt(document.getElementById('num2').value);
                var operation = document.getElementById('operation').value;

                var result = window.external.Calculate(num1, num2, operation);

                var operationSymbol = {
                    'add': '+',
                    'subtract': '-',
                    'multiply': '×',
                    'divide': '÷'
                };

                document.getElementById('calcResult').innerHTML =
                    num1 + ' ' + operationSymbol[operation] + ' ' + num2 + ' = ' + result;
            } catch (e) {
                document.getElementById('calcResult').innerHTML = '计算失败: ' + e.message;
            }
        }

        // JavaScript调用C#方法：更改窗体标题
        function changeTitle() {
            try {
                var title = document.getElementById('titleInput').value;
                if (title.trim()) {
                    window.external.SetWindowTitle(title);
                    showMessage('窗体标题已更改为: ' + title, 'success');
                } else {
                    alert('请输入标题！');
                }
            } catch (e) {
                alert('更改标题失败: ' + e.message);
            }
        }

        // C#调用的JavaScript方法：显示消息
        function showMessage(message, type) {
            var messageDiv = document.getElementById('messageFromCSharp');
            var timestamp = new Date().toLocaleString('zh-CN');

            var color = type === 'success' ? '#90EE90' : '#FFB6C1';
            var icon = type === 'success' ? '✅' : '📝';

            messageDiv.innerHTML = icon + ' [' + timestamp + '] ' + message;
            messageDiv.style.color = color;
        }

        // 页面加载完成
        window.onload = function() {
            console.log('HTML页面已加载完成');
        };
    </script>
</body>
</html>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime(); // 立即显示时间
        
        // 交互功能
        function showAlert() {
            alert('WebBrowser控件JavaScript功能正常工作！');
        }
        
        function changeBackground() {
            const colors = [
                'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
            ];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            document.body.style.background = randomColor;
        }
        
        function addContent() {
            const content = document.getElementById('dynamicContent');
            const newElement = document.createElement('div');
            newElement.className = 'feature-box';
            newElement.innerHTML = `
                <h3>🎉 动态添加的内容</h3>
                <p>添加时间: ${new Date().toLocaleString('zh-CN')}</p>
                <p>这个内容是通过JavaScript动态添加的，证明WebBrowser控件完全支持JavaScript功能。</p>
            `;
            content.appendChild(newElement);
        }
        
        function displayInput() {
            const input = document.getElementById('userInput').value;
            if (input.trim()) {
                const content = document.getElementById('dynamicContent');
                const newElement = document.createElement('div');
                newElement.className = 'feature-box';
                newElement.innerHTML = `
                    <h3>💬 用户输入</h3>
                    <p>您输入的内容: <strong>${input}</strong></p>
                    <p>输入时间: ${new Date().toLocaleString('zh-CN')}</p>
                `;
                content.appendChild(newElement);
                document.getElementById('userInput').value = '';
            } else {
                alert('请先输入一些文字！');
            }
        }
        
        // ===== C#后端通信功能 =====

        // 从C#接收消息的函数
        function receiveMessageFromCSharp(message, type) {
            const messagesDiv = document.getElementById('csharpMessages');
            const timestamp = new Date().toLocaleString('zh-CN');

            let color = '#fff';
            let icon = '📝';
            switch(type) {
                case 'success': color = '#90EE90'; icon = '✅'; break;
                case 'error': color = '#FFB6C1'; icon = '❌'; break;
                case 'warning': color = '#FFD700'; icon = '⚠️'; break;
                default: color = '#87CEEB'; icon = '📝'; break;
            }

            const messageElement = document.createElement('div');
            messageElement.style.color = color;
            messageElement.style.marginBottom = '5px';
            messageElement.innerHTML = `${icon} [${timestamp}] ${message}`;

            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 更新服务器时间
        function updateServerTime(time) {
            document.getElementById('serverTime').textContent = time;
        }

        // C#调用的背景改变函数
        function changeBackgroundFromCSharp() {
            changeBackground();
            receiveMessageFromCSharp('背景已由C#后端更改', 'success');
        }

        // C#请求数据时的响应函数
        function requestDataFromCSharp() {
            const data = {
                pageTitle: document.title,
                currentTime: new Date().toLocaleString('zh-CN'),
                userAgent: navigator.userAgent,
                screenSize: `${screen.width}x${screen.height}`,
                windowSize: `${window.innerWidth}x${window.innerHeight}`
            };

            let dataString = '';
            for (let key in data) {
                dataString += `${key}: ${data[key]}\n`;
            }

            receiveMessageFromCSharp('页面数据:\n' + dataString, 'info');
        }

        // ===== 连接测试函数 =====

        function testCSharpConnection() {
            receiveMessageFromCSharp('正在测试C#连接...', 'info');

            // 检查window.external是否可用
            if (typeof window.external === 'undefined') {
                receiveMessageFromCSharp('错误: window.external 未定义', 'error');
                return false;
            }

            // 检查ShowMessage方法是否可用
            if (typeof window.external.ShowMessage !== 'function') {
                receiveMessageFromCSharp('错误: ShowMessage 方法不可用', 'error');
                return false;
            }

            receiveMessageFromCSharp('C#连接测试成功！', 'success');
            return true;
        }

        // ===== JavaScript调用C#的函数 =====

        function callCSharpShowMessage() {
            try {
                // 先测试连接
                if (!testCSharpConnection()) {
                    return;
                }

                window.external.ShowMessage('这是从JavaScript发送的消息！');
                receiveMessageFromCSharp('成功调用C#消息框', 'success');
            } catch (e) {
                receiveMessageFromCSharp('调用C#方法失败: ' + e.message, 'error');
                console.error('调用C#方法失败:', e);
            }
        }

        function callCSharpGetSystemInfo() {
            try {
                if (typeof window.external === 'undefined' || typeof window.external.GetSystemInfo !== 'function') {
                    receiveMessageFromCSharp('错误: GetSystemInfo 方法不可用', 'error');
                    return;
                }

                const info = window.external.GetSystemInfo();
                receiveMessageFromCSharp('系统信息:\n' + info, 'info');
            } catch (e) {
                receiveMessageFromCSharp('获取系统信息失败: ' + e.message, 'error');
                console.error('获取系统信息失败:', e);
            }
        }

        function callCSharpGetAppInfo() {
            try {
                if (typeof window.external === 'undefined' || typeof window.external.GetAppInfo !== 'function') {
                    receiveMessageFromCSharp('错误: GetAppInfo 方法不可用', 'error');
                    return;
                }

                const info = window.external.GetAppInfo();
                receiveMessageFromCSharp('应用信息:\n' + info, 'info');
            } catch (e) {
                receiveMessageFromCSharp('获取应用信息失败: ' + e.message, 'error');
                console.error('获取应用信息失败:', e);
            }
        }

        function sendMessageToCSharp() {
            const input = document.getElementById('csharpInput');
            const message = input.value.trim();
            if (message) {
                try {
                    window.external.LogMessage('来自JavaScript: ' + message);
                    receiveMessageFromCSharp('消息已发送到C#日志: ' + message, 'success');
                    input.value = '';
                } catch (e) {
                    receiveMessageFromCSharp('发送消息失败: ' + e.message, 'error');
                }
            } else {
                alert('请输入要发送的消息！');
            }
        }

        function callCSharpChangeTitle() {
            const newTitle = prompt('请输入新的窗体标题:', '新标题 - ' + new Date().toLocaleTimeString());
            if (newTitle) {
                try {
                    window.external.ChangeFormTitle(newTitle);
                    receiveMessageFromCSharp('窗体标题已更改为: ' + newTitle, 'success');
                } catch (e) {
                    receiveMessageFromCSharp('更改标题失败: ' + e.message, 'error');
                }
            }
        }

        function callCSharpGetRandom() {
            try {
                const min = 1;
                const max = 100;
                const randomNum = window.external.GetRandomNumber(min, max);
                receiveMessageFromCSharp(`随机数 (${min}-${max}): ${randomNum}`, 'success');
            } catch (e) {
                receiveMessageFromCSharp('获取随机数失败: ' + e.message, 'error');
            }
        }

        // 检查C#连接状态
        function checkCSharpConnection() {
            setTimeout(function() {
                if (typeof window.external === 'undefined') {
                    receiveMessageFromCSharp('警告: C#后端连接不可用，请检查ObjectForScripting设置', 'warning');
                } else if (typeof window.external.ShowMessage !== 'function') {
                    receiveMessageFromCSharp('警告: C#方法不可用，请检查ScriptInterface类', 'warning');
                } else {
                    receiveMessageFromCSharp('C#后端连接正常，所有功能可用', 'success');
                }
            }, 1000); // 延迟1秒检查，确保页面完全加载
        }

        // 页面加载完成提示
        window.onload = function() {
            console.log('HTML页面已成功加载到WebBrowser控件中！');
            receiveMessageFromCSharp('页面加载完成，正在检查C#后端连接...', 'info');
            checkCSharpConnection();
        };
    </script>
</body>
</html>
