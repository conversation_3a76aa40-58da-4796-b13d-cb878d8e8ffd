# 简单的HTML页面与C#后端交互示例

## 项目概述

这是一个简化的WebBrowser控件示例，展示了HTML页面与C# WinForms应用程序之间的基本双向通信。

## 功能特点

### 1. JavaScript调用C#方法
- **显示消息框**: JavaScript调用C#的ShowAlert方法
- **获取当前时间**: JavaScript调用C#的GetCurrentTime方法
- **计算器功能**: JavaScript调用C#的Calculate方法进行数学运算
- **窗体控制**: JavaScript调用C#的SetWindowTitle方法更改窗体标题

### 2. C#调用JavaScript方法
- **发送消息**: C#通过showMessage函数向HTML页面发送消息
- **按钮交互**: C#窗体上的按钮可以向HTML页面发送数据

## 核心代码结构

### C#后端 (Form1.cs)
```csharp
// 设置脚本接口
webBrowser1.ObjectForScripting = scriptInterface;

// C#调用JavaScript
webBrowser1.Document.InvokeScript(functionName, args);
```

### JavaScript前端 (index.html)
```javascript
// JavaScript调用C#
window.external.ShowAlert(message);
window.external.GetCurrentTime();
window.external.Calculate(a, b, operation);
window.external.SetWindowTitle(title);

// C#调用的JavaScript函数
function showMessage(message, type) {
    // 显示来自C#的消息
}
```

## 交互流程

1. **页面加载**: WebBrowser控件加载index.html
2. **连接建立**: 设置ObjectForScripting，建立双向通信
3. **用户交互**: 
   - 点击HTML按钮 → JavaScript调用C#方法
   - 点击C#按钮 → C#调用JavaScript方法

## 使用方法

1. 编译并运行WebControlSwitching项目
2. 确保index.html文件在应用程序目录中
3. 在HTML页面中测试各种交互功能
4. 在C#窗体上的按钮测试向HTML发送消息

## 关键技术点

- **[ComVisible(true)]**: 使C#类对JavaScript可见
- **ObjectForScripting**: WebBrowser控件的脚本接口属性
- **window.external**: JavaScript访问C#对象的接口
- **InvokeScript**: C#调用JavaScript函数的方法

## 与复杂版本的区别

相比WindowsFormsApplication7项目，这个简化版本：
- 删除了复杂的游戏逻辑
- 简化了HTML界面设计
- 保留了核心的双向通信机制
- 提供了清晰的交互示例

这个示例非常适合学习和理解WebBrowser控件的基本用法。
