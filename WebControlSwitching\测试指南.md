# WebControlSwitching 简单交互测试指南

## 项目简介

这是一个简化版的HTML页面与C# WinForms应用程序双向通信示例，基于WindowsFormsApplication7项目的核心交互机制，但删除了复杂的游戏逻辑，只保留最基本的交互功能。

## 运行方法

1. 打开Visual Studio
2. 加载 `WebControlSwitching.sln` 解决方案
3. 编译并运行项目
4. 应用程序将自动加载HTML页面

## 测试功能

### 1. JavaScript调用C#功能测试

#### 1.1 显示消息框
- 点击HTML页面中的"显示C#消息框"按钮
- 应该弹出一个Windows消息框，显示"这是从JavaScript发送到C#的消息！"

#### 1.2 获取当前时间
- 点击"获取C#当前时间"按钮
- 页面应该显示从C#后端获取的当前时间

#### 1.3 计算器功能
- 在数字输入框中输入两个数字
- 选择运算符（+、-、×、÷）
- 点击"计算"按钮
- 页面应该显示计算结果

#### 1.4 窗体标题控制
- 在标题输入框中输入新标题
- 点击"更改窗体标题"按钮
- 应用程序窗体的标题应该改变

### 2. C#调用JavaScript功能测试

#### 2.1 发送消息到HTML
- 在C#窗体顶部的文本框中输入消息
- 点击"发送消息"按钮
- HTML页面底部的"C#到JavaScript通信"区域应该显示消息

#### 2.2 其他C#按钮
- 点击"改变背景"按钮：向HTML发送背景更改消息
- 点击"获取数据"按钮：向HTML发送当前时间信息

## 核心技术实现

### C#端关键代码
```csharp
// 设置脚本接口
webBrowser1.ObjectForScripting = scriptInterface;

// C#调用JavaScript
webBrowser1.Document.InvokeScript("showMessage", message, "success");
```

### JavaScript端关键代码
```javascript
// JavaScript调用C#
window.external.ShowAlert(message);
window.external.GetCurrentTime();
window.external.Calculate(a, b, operation);
window.external.SetWindowTitle(title);
```

## 故障排除

### 常见问题

1. **页面显示"找不到HTML文件"**
   - 确保 `index.html` 文件在应用程序的输出目录中
   - 检查 `bin\Debug\index.html` 是否存在

2. **JavaScript调用C#失败**
   - 确保 `ScriptInterface` 类标记了 `[ComVisible(true)]`
   - 检查 `ObjectForScripting` 是否正确设置

3. **C#调用JavaScript失败**
   - 确保页面完全加载后再调用JavaScript函数
   - 检查JavaScript函数名是否正确

### 调试技巧

1. 在C#代码中设置断点，观察方法调用
2. 在浏览器开发者工具中查看JavaScript错误
3. 检查控制台输出的错误信息

## 与原项目的对比

| 功能 | WindowsFormsApplication7 | WebControlSwitching |
|------|-------------------------|-------------------|
| 复杂度 | 高（游戏逻辑） | 低（基本交互） |
| 文件数量 | 多个HTML页面 | 单个HTML页面 |
| 交互类型 | 游戏功能 | 基础演示 |
| 学习难度 | 高 | 低 |

## 扩展建议

基于这个简单示例，你可以：

1. 添加更多的C#方法供JavaScript调用
2. 实现更复杂的数据传递（JSON格式）
3. 添加文件操作、数据库操作等功能
4. 创建更美观的HTML界面
5. 添加错误处理和日志记录

这个简化版本非常适合初学者理解WebBrowser控件的双向通信机制。
