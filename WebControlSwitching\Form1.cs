﻿using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace WebControlSwitching
{
    public partial class Form1 : Form
    {
        private ScriptInterface scriptInterface;

        public Form1()
        {
            InitializeComponent();
            scriptInterface = new ScriptInterface(this);
            InitializeWebBrowser();
            LoadHtmlPage();
        }

        private void InitializeWebBrowser()
        {
            // 设置WebBrowser控件的脚本接口
            webBrowser1.ObjectForScripting = scriptInterface;

            // 禁用脚本错误提示
            webBrowser1.ScriptErrorsSuppressed = true;

            // 订阅DocumentCompleted事件
            webBrowser1.DocumentCompleted += WebBrowser1_DocumentCompleted;
        }

        private void WebBrowser1_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            // 页面加载完成后，重新设置脚本接口
            webBrowser1.ObjectForScripting = scriptInterface;

            // 向JavaScript发送连接成功消息
            CallJavaScriptFunction("showMessage", "C#后端连接成功！", "success");
        }

        private void LoadHtmlPage()
        {
            try
            {
                string htmlPath = Path.Combine(Application.StartupPath, "index.html");

                if (File.Exists(htmlPath))
                {
                    webBrowser1.Navigate(htmlPath);
                }
                else
                {
                    webBrowser1.DocumentText = @"
                    <html>
                    <body style='font-family: Arial; padding: 20px;'>
                        <h2 style='color: red;'>错误：找不到 index.html 文件</h2>
                        <p>请确保 index.html 文件位于应用程序目录中。</p>
                    </body>
                    </html>";
                }
            }
            catch (Exception ex)
            {
                webBrowser1.DocumentText = $@"
                <html>
                <body style='font-family: Arial; padding: 20px;'>
                    <h2 style='color: red;'>加载页面时发生错误</h2>
                    <p>错误信息: {ex.Message}</p>
                </body>
                </html>";
            }
        }

        // C#调用JavaScript函数
        private void CallJavaScriptFunction(string functionName, params object[] args)
        {
            try
            {
                if (webBrowser1.Document != null)
                {
                    webBrowser1.Document.InvokeScript(functionName, args);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"调用JavaScript函数失败: {ex.Message}");
            }
        }

        // 按钮事件处理程序
        private void btnSendMessage_Click(object sender, EventArgs e)
        {
            string message = txtMessage.Text;
            if (!string.IsNullOrEmpty(message))
            {
                CallJavaScriptFunction("showMessage", message, "success");
            }
        }

        private void btnChangeBackground_Click(object sender, EventArgs e)
        {
            // 改变HTML页面背景色
            CallJavaScriptFunction("showMessage", "背景已由C#按钮更改", "success");
        }

        private void btnGetData_Click(object sender, EventArgs e)
        {
            // 获取当前时间并发送到JavaScript
            string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            CallJavaScriptFunction("showMessage", "当前时间: " + currentTime, "success");
        }
    }

    // JavaScript调用C#的接口类
    [ComVisible(true)]
    public class ScriptInterface
    {
        private Form1 mainForm;

        public ScriptInterface(Form1 form)
        {
            mainForm = form;
        }

        // 显示消息框
        public void ShowAlert(string message)
        {
            MessageBox.Show(message, "来自JavaScript", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // 获取当前时间
        public string GetCurrentTime()
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        // 简单的计算功能
        public int Calculate(int a, int b, string operation)
        {
            switch (operation.ToLower())
            {
                case "add": return a + b;
                case "subtract": return a - b;
                case "multiply": return a * b;
                case "divide": return b != 0 ? a / b : 0;
                default: return 0;
            }
        }

        // 改变窗体标题
        public void SetWindowTitle(string title)
        {
            if (mainForm != null)
            {
                mainForm.Text = title;
            }
        }
    }
}
